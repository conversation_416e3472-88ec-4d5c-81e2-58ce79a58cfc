import csv
from requests import Session
from bs4 import BeautifulSoup
from datetime import datetime
from pathlib import Path
import argparse
import concurrent.futures
import threading
import re

lock = threading.Lock()

def find_latest_csv():
    output_dir = Path("outputs")
    csv_files = sorted(output_dir.glob("**/electors_master_*.csv"), key=lambda f: f.stat().st_mtime, reverse=True)
    return csv_files[0] if csv_files else None

def perform_lookup(session, first_name, last_name, month, year):
    url = f"https://ebctt.com/electoral-process/registration-look-up/?firstname={first_name}&lastname={last_name}&mob={month}&yob={year}&list=all&check-registration=Check+Registration+%26+Polling+Station"
    try:
        response = session.get(url, timeout=10)
        soup = BeautifulSoup(response.text, "html.parser")
        result_p = soup.select_one(".lookup_results p")
        if result_p:
            text = result_p.text.strip()
            num_match = re.search(r'(\d+)', text)
            count = int(num_match.group(1)) if num_match else 0
            if count > 0:
                details = [li.get_text(strip=True) for li in soup.select(".lookup_results ul li")]
                return True, {"count": count, "details": details or [text]}
        return False, None
    except Exception as e:
        print(f"❌ Error for {first_name} {last_name} {month}/{year}: {e}")
        return False, None

def main(name_fragment, start_year, end_year):
    csv_file = find_latest_csv()
    if not csv_file:
        print("No CSV source file found.")
        return

    print(f"Using source file: {csv_file}")
    matches = []

    with open(csv_file, newline='') as f:
        reader = csv.DictReader(f)
        relevant_rows = [row for row in reader if name_fragment.lower() in row["Surname"].lower() or name_fragment.lower() in row["Given Names"].lower()]

    session = Session()
    tasks = {}

    from concurrent.futures import ThreadPoolExecutor, as_completed

    with ThreadPoolExecutor(max_workers=20) as executor:
        # Prepare all lookup tasks
        for row in relevant_rows:
            first_name = row["Given Names"].split()[0]
            last_name = row["Surname"]
            for year in range(start_year, end_year + 1):
                for month in range(1, 13):
                    future = executor.submit(perform_lookup, session, first_name, last_name, month, year)
                    tasks[future] = (row, month, year, first_name, last_name)

        for future in as_completed(tasks):
            row, month, year, first_name, last_name = tasks[future]
            matched, result = future.result()
            if matched:
                count = result["count"]
                for detail in result["details"]:
                    print(f"✅ Match: {first_name} {last_name} ({month}/{year}) - {detail}")
                    with lock:
                        matches.append({
                            "Polling Division": row["Polling Division"],
                            "Surname": row["Surname"],
                            "Given Names": row["Given Names"],
                            "Address": row["Address"],
                            "Serial Number": row["Serial Number"],
                            "Source File": row["Source File"],
                            "Month": month,
                            "Year": year,
                            "Num Results": count,
                            "Detail": detail
                        })
    session.close()

    if matches:
        # Write all relevant matches to a single output file
        output_filename = f"{name_fragment}_{start_year}_{end_year}_csv_lookup_matches.csv"
        with open(output_filename, "w", newline="") as out_csv:
            fieldnames = [
                "Polling Division", "Surname", "Given Names",
                "Address", "Serial Number", "Source File",
                "Month", "Year", "Num Results", "Detail"
            ]
            writer = csv.DictWriter(out_csv, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(matches)
        print(f"\nSaved {len(matches)} matches to {output_filename}")
    else:
        print("No matches found.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--name", required=True, help="Partial name to match in the CSV")
    parser.add_argument("--start_year", type=int, default=1980, help="Start year for lookup")
    parser.add_argument("--end_year", type=int, default=datetime.now().year, help="End year for lookup")
    args = parser.parse_args()

    main(args.name, args.start_year, args.end_year)