import fitz  # PyMuPDF
import csv
import re
from pathlib import Path
from datetime import datetime

def extract_text_from_page(page):
    """Extract text from a PDF page using word-based method to preserve layout."""
    words = page.get_text('words')

    # Group words by approximate y-coordinate (same line)
    lines_dict = {}
    for word in words:
        x0, y0, _, _, text, _, _, _ = word  # Only use x0, y0, and text
        y_key = round(y0, 1)  # Round to group words on same line
        if y_key not in lines_dict:
            lines_dict[y_key] = []
        lines_dict[y_key].append((x0, text))

    # Sort lines by y-coordinate and words by x-coordinate
    sorted_lines = []
    for y in sorted(lines_dict.keys()):
        words_on_line = sorted(lines_dict[y], key=lambda x: x[0])
        line_text = ' '.join([word[1] for word in words_on_line])
        sorted_lines.append(line_text)

    return sorted_lines

def extract_polling_division_from_filename(filename):
    """Extract polling division number from filename like PD0005.PDF."""
    match = re.search(r'PD(\d{4})\.PDF', filename, re.IGNORECASE)
    if match:
        return match.group(1)
    return "UNKNOWN"

def extract_district_info(page):
    """Extract Parliamentary Electoral District and Electoral District from the first page."""
    text = page.get_text()
    lines = text.split('\n')

    parliamentary_district = "UNKNOWN"
    electoral_district = "UNKNOWN"

    for line in lines:
        line = line.strip()

        # Look for Parliamentary Electoral District
        if 'PARLIAMENTARY ELECTORAL DISTRICT:' in line.upper():
            match = re.search(r'PARLIAMENTARY ELECTORAL DISTRICT:\s*(.+)', line, re.IGNORECASE)
            if match:
                parliamentary_district = match.group(1).strip().title()

        # Look for Electoral District (but not Parliamentary)
        elif ('ELECTORAL DISTRICT :' in line.upper() and
              'PARLIAMENTARY' not in line.upper()):
            match = re.search(r'ELECTORAL DISTRICT\s*:\s*(.+)', line, re.IGNORECASE)
            if match:
                electoral_district = match.group(1).strip().title()

    return parliamentary_district, electoral_district

pdf_dir = Path(__file__).parent / "2024"
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_dir = Path("outputs/" + timestamp)
output_dir.mkdir(exist_ok=True)
csv_path = output_dir / f"electors_master_{timestamp}.csv"
error_log_path = output_dir / f"electors_errors_{timestamp}.log"

csv_file = open(csv_path, mode="w", newline="", encoding="utf-8")
csv_writer = csv.writer(csv_file)
csv_writer.writerow(["Polling Division", "Surname", "Given Names", "Address", "Parliamentary Electoral District", "Electoral District", "Serial Number", "Source File"])

error_log = open(error_log_path, mode="w", encoding="utf-8")

# Create unmatched CSV file and writer
unmatched_csv_path = output_dir / f"unmatched_{timestamp}.csv"
unmatched_csv_file = open(unmatched_csv_path, mode="w", newline="", encoding="utf-8")
unmatched_writer = csv.writer(unmatched_csv_file)
unmatched_writer.writerow(["Source File", "Polling Division", "Unmatched Line"])

# Look for both .pdf and .PDF files (case insensitive)
pdf_files = list(pdf_dir.glob("*.pdf")) + list(pdf_dir.glob("*.PDF"))
print(f"Found {len(pdf_files)} PDF files in {pdf_dir}")

# Initialise summary variables
total_lines = 0
matched_lines = 0
unmatched_lines = 0

def parse_voter_line(line):
    """Parse a line containing voter information in two-column format."""
    line = line.strip()

    # Skip obvious header/non-data lines
    if not line or len(line) < 20:
        return []

    # Skip lines that don't contain voter data
    if not ',' in line:
        return []

    # Skip boundary descriptions and other non-voter text
    skip_patterns = [
        'POLLING DIVISION', 'PAGE', 'CONSEC', 'APT/LOT', 'BLK/BLD',
        'STREET NAME', 'HOUSE', 'PHASE', 'NUMBER', 'NAME',
        'THENCE', 'ALONG', 'BOUNDARY', 'RIVER', 'CHANNEL', 'TRACE TO',
        'CONTINUING', 'SAID', 'LANDS OF'
    ]

    if any(pattern in line.upper() for pattern in skip_patterns):
        return []

    voters = []

    # The data is in two-column format: "SURNAME1, GIVEN_NAMES1 ADDRESS1 SURNAME2, GIVEN_NAMES2 ADDRESS2"
    # We need to find where the first voter record ends and the second begins

    # Find all comma positions that could be part of names
    comma_positions = []
    for i, char in enumerate(line):
        if char == ',':
            # Check if this comma is part of a name (has letters before and after)
            if (i > 0 and i < len(line) - 1 and
                line[i-1].isalpha() and line[i+1].isspace() and
                i+2 < len(line) and line[i+2].isalpha()):
                comma_positions.append(i)

    if len(comma_positions) >= 2:
        # We have at least 2 names, try to split into two voter records

        # Find the split point between the two records
        # Look for the pattern where we have "ADDRESS SURNAME,"
        # The split should be just before the second surname

        first_comma = comma_positions[0]
        second_comma = comma_positions[1]

        # Find where the first address ends by looking for the start of the second name
        # Look backwards from the second comma to find where the address ends
        split_point = None

        # Look for a pattern like "WORD SURNAME," where SURNAME is all caps
        for i in range(second_comma - 1, first_comma, -1):
            if line[i].isspace():
                # Check if the next word (before the comma) is all caps (likely a surname)
                word_start = i + 1
                word_end = second_comma
                potential_surname = line[word_start:word_end].strip()

                if (potential_surname and potential_surname.isupper() and
                    len(potential_surname) > 2 and potential_surname.isalpha()):
                    split_point = i
                    break

        if split_point:
            # Split the line into two voter records
            first_record = line[:split_point].strip()
            second_record = line[split_point:].strip()

            # Parse each record
            for record in [first_record, second_record]:
                if ',' in record:
                    # Find the comma that separates surname from given names
                    comma_idx = record.find(',')
                    if comma_idx > 0:
                        # Extract name part (everything up to the end of given names)
                        # Look for where the address starts (usually after given names)
                        name_end = comma_idx

                        # Find the end of the given names (look for address indicators)
                        for i in range(comma_idx + 1, len(record)):
                            if (record[i].isdigit() or
                                record[i:i+2] == 'LP' or
                                record[i:i+4] == 'UNIT' or
                                record[i:i+3] == 'LOT' or
                                record[i:i+4] == 'BLK ' or
                                record[i:i+2] == '# '):
                                name_end = i
                                break
                            elif i > comma_idx + 50:  # Prevent runaway
                                name_end = comma_idx + 30
                                break

                        name_part = record[:name_end].strip()
                        address_part = record[name_end:].strip()

                        if ',' in name_part and address_part:
                            try:
                                surname, given_names = [p.strip().title() for p in name_part.split(',', 1)]
                                if surname and given_names and address_part:
                                    voters.append((surname, given_names, address_part.title()))
                            except:
                                pass  # Skip malformed names

    return voters

for pdf_file in pdf_files:
    try:
        print(f"Processing {pdf_file.name}...")

        # Extract polling division from filename
        polling_division = extract_polling_division_from_filename(pdf_file.name)

        with fitz.open(pdf_file) as doc:
            # Extract district information from the first page
            parliamentary_district, electoral_district = extract_district_info(doc[0])

            all_lines = []
            for page_num, page in enumerate(doc):
                page_lines = extract_text_from_page(page)
                all_lines.extend(page_lines)

            print(f"Extracted {len(all_lines)} lines from {len(doc)} pages in {pdf_file.name}")
            print(f"Districts: Parliamentary='{parliamentary_district}', Electoral='{electoral_district}'")

            # Process each line
            for line in all_lines:
                line = line.strip()

                # Skip empty lines (detailed filtering is done in parse_voter_line)
                if not line or len(line) < 10:
                    continue

                total_lines += 1

                # Try to parse voter information (may return multiple voters per line)
                voters = parse_voter_line(line)
                if voters:
                    for surname, given_names, address in voters:
                        # Generate a sequential number for this voter (no serial numbers in this format)
                        serial_number = f"{matched_lines + 1:04d}"
                        row = [polling_division, surname, given_names, address, parliamentary_district, electoral_district, serial_number, pdf_file.name]
                        csv_writer.writerow(row)
                        matched_lines += 1

                        if matched_lines <= 10:  # Show first 10 matches as samples
                            print(f"Sample parsed entry: {row}")
                else:
                    unmatched_lines += 1
                    unmatched_writer.writerow([pdf_file.name, polling_division, line])

    except Exception as e:
        error_msg = f"FAILED TO PROCESS {pdf_file.name}: {str(e)}\n"
        error_log.write(error_msg)
        print(f"Error processing {pdf_file.name}: {e}")

print(f"--- Parsing Summary ---")
print(f"Total lines processed: {total_lines}")
print(f"Matched entries: {matched_lines}")
print(f"Unmatched lines: {unmatched_lines}")
if total_lines > 0:
    print(f"Match rate: {100 * matched_lines / total_lines:.2f}%")
else:
    print("Match rate: N/A (no lines processed)")

csv_file.close()
error_log.close()
unmatched_csv_file.close()